@props(['paginator'])

@if ($paginator->hasPages())
    <nav aria-label="Page navigation">
        <div class="pagination-container position-relative">
            <ul class="pagination mb-0 flex-nowrap pagination-seamless">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <li class="page-item disabled" aria-disabled="true">
                    <span class="page-link">
                        <i class="bx bx-chevron-left"></i>
                    </span>
                </li>
            @else
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                        <i class="bx bx-chevron-left"></i>
                    </a>
                </li>
            @endif

            {{-- Pagination Elements --}}
            @php
                // Use CSS-based responsive behavior instead of server-side detection
                $start = max(1, $paginator->currentPage() - 2);
                $end = min($paginator->lastPage(), $paginator->currentPage() + 2);
            @endphp

            {{-- First page --}}
            @if ($start > 1)
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->url(1) }}">1</a>
                </li>
                @if ($start > 2)
                    <li class="page-item disabled" aria-disabled="true">
                        <span class="page-link">
                            <i class="bx bx-dots-horizontal-rounded"></i>
                        </span>
                    </li>
                @endif
            @endif

            {{-- Page range --}}
            @for ($page = $start; $page <= $end; $page++)
                @php
                    // Add responsive classes to hide outer pages on mobile
                    $isOuterPage = ($page == $start && $page < $paginator->currentPage() - 1) ||
                                   ($page == $end && $page > $paginator->currentPage() + 1);
                    $responsiveClass = $isOuterPage ? 'd-none d-sm-block' : '';
                @endphp
                @if ($page == $paginator->currentPage())
                    <li class="page-item active {{ $responsiveClass }}" aria-current="page">
                        <span class="page-link">{{ $page }}</span>
                    </li>
                @else
                    <li class="page-item {{ $responsiveClass }}">
                        <a class="page-link" href="{{ $paginator->url($page) }}">{{ $page }}</a>
                    </li>
                @endif
            @endfor

            {{-- Last page --}}
            @if ($end < $paginator->lastPage())
                @if ($end < $paginator->lastPage() - 1)
                    <li class="page-item disabled" aria-disabled="true">
                        <span class="page-link">
                            <i class="bx bx-dots-horizontal-rounded"></i>
                        </span>
                    </li>
                @endif
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->url($paginator->lastPage()) }}">{{ $paginator->lastPage() }}</a>
                </li>
            @endif

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->nextPageUrl() }}" rel="next">
                        <i class="bx bx-chevron-right"></i>
                    </a>
                </li>
            @else
                <li class="page-item disabled" aria-disabled="true">
                    <span class="page-link">
                        <i class="bx bx-chevron-right"></i>
                    </span>
                </li>
            @endif
        </ul>
        </div>
    </nav>

    {{-- Seamless Pagination Styling --}}
    <style>
        .pagination-seamless .page-item {
            margin: 0 !important;
        }

        .pagination-seamless .page-item .page-link {
            border-radius: 0 !important;
            border-right: 0;
            min-width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.375rem 0.75rem;
            font-size: 0.8125rem;
            color: var(--default-text-color, #6c757d);
            background-color: var(--custom-white, #fff);
            border: 1px solid var(--default-border, #dee2e6);
            transition: all 0.15s ease-in-out;
        }

        .pagination-seamless .page-item:first-child .page-link {
            border-top-left-radius: 0.375rem !important;
            border-bottom-left-radius: 0.375rem !important;
        }

        .pagination-seamless .page-item:last-child .page-link {
            border-top-right-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
            border-right: 1px solid var(--default-border, #dee2e6);
        }

        .pagination-seamless .page-item.active .page-link {
            background-color: rgb(var(--secondary-rgb));
            border-color: rgb(var(--secondary-rgb));
            color: white;
            border-right: 1px solid rgb(var(--secondary-rgb));
        }

        .pagination-seamless .page-item:hover .page-link:not(.active) {
            background-color: var(--bs-light, #f8f9fa);
            color: rgb(var(--secondary-rgb));
            border-color: var(--default-border, #dee2e6);
        }

        .pagination-seamless .page-item.disabled .page-link {
            opacity: 0.6;
            pointer-events: none;
            color: var(--default-text-color, #6c757d);
            background-color: var(--custom-white, #fff);
            border-color: var(--default-border, #dee2e6);
        }

        /* Dark Mode Styles */
        [data-theme-mode="dark"] .pagination-seamless .page-item .page-link {
            color: var(--default-text-color);
            background-color: var(--custom-white);
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .pagination-seamless .page-item:last-child .page-link {
            border-right: 1px solid var(--default-border);
        }

        [data-theme-mode="dark"] .pagination-seamless .page-item.active .page-link {
            background-color: rgb(var(--secondary-rgb));
            border-color: rgb(var(--secondary-rgb));
            color: white;
            border-right: 1px solid rgb(var(--secondary-rgb));
        }

        [data-theme-mode="dark"] .pagination-seamless .page-item:hover .page-link:not(.active) {
            background-color: rgb(var(--light-rgb));
            color: rgb(var(--secondary-rgb));
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .pagination-seamless .page-item.disabled .page-link {
            opacity: 0.6;
            pointer-events: none;
            color: var(--default-text-color);
            background-color: var(--custom-white);
            border-color: var(--default-border);
        }

        /* Pagination container with horizontal scroll fallback */
        .pagination-seamless {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE/Edge */
        }

        .pagination-seamless::-webkit-scrollbar {
            display: none; /* Chrome/Safari */
        }

        /* Responsive adjustments - maintain touch-friendly sizes */
        @media (max-width: 768px) {
            .pagination-seamless .page-item .page-link {
                min-width: 2.25rem;
                height: 2.25rem;
                font-size: 0.8rem;
                padding: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .pagination-seamless .page-item .page-link {
                min-width: 2.25rem;
                height: 2.25rem;
                font-size: 0.75rem;
                padding: 0.4rem;
            }

            /* Add subtle scroll indicator */
            .pagination-container::after {
                content: '';
                position: absolute;
                right: 0;
                top: 0;
                bottom: 0;
                width: 20px;
                background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
                pointer-events: none;
                z-index: 1;
            }

            [data-theme-mode="dark"] .pagination-container::after {
                background: linear-gradient(to left, rgba(0,0,0,0.8), transparent);
            }
        }
    </style>
@endif
